import logging
from telegram import Update, constants, BotCommand, BotCommandScopeAllPrivateChats
from telegram.ext import (
    Application, CommandHandler, MessageHandler,
    filters, ContextTypes, Defaults
)

from agno.tools.mcp import MultiMCPTools
from src.ai.agent import AIAgent
from src.utils.config import TELEGRAM_TOKEN
from src.utils.simple_scheduler import set_message_callback, start_scheduler, stop_scheduler
from src.telegram.commands import start_command, clear_command, clear_memory_command
from src.telegram.commands.add_sticker import add_sticker_conv_handler # Added import
from src.telegram.handlers import handle_message

logger = logging.getLogger(__name__)


class TelegramBot:
    def __init__(self, ai_agent: AIAgent, mcp_tools: MultiMCPTools):
        self.ai_agent = ai_agent
        self.mcp_tools = mcp_tools

        defaults = Defaults(parse_mode=constants.ParseMode.HTML)

        self.application = (
            Application.builder()
            .token(TELEGRAM_TOKEN)
            .defaults(defaults)
            .connect_timeout(30.0)     # Increase connection timeout to 30 seconds
            .read_timeout(30.0)        # Increase read timeout to 30 seconds
            .write_timeout(30.0)       # Increase write timeout to 30 seconds
            # .media_read_timeout(60.0)  # Increase media read timeout to 60 seconds
            .media_write_timeout(60.0) # Increase media write timeout to 60 seconds
            .pool_timeout(15.0)        # Increase pool timeout to 15 seconds
            .post_init(self.post_init)
            .build()
        )

        self.application.bot_data['ai_agent'] = self.ai_agent
        self.application.bot_data['mcp_tools'] = self.mcp_tools

        # Set up simple scheduler callback for sending proactive messages
        set_message_callback(self._send_proactive_message)

        self._register_handlers()

    def _register_handlers(self):
        self.application.add_handler(CommandHandler("start", start_command))
        self.application.add_handler(CommandHandler("clear", clear_command))
        self.application.add_handler(CommandHandler("clear_memory", clear_memory_command))
        self.application.add_handler(add_sticker_conv_handler) # Added handler

        # Handle text messages (all chat types)
        self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_message))
        
        # Handle photo messages (with or without caption)
        self.application.add_handler(MessageHandler(filters.PHOTO, handle_message))
        
        # Handle document/file messages (PDFs, etc.)
        self.application.add_handler(MessageHandler(filters.Document.ALL, handle_message))
        
        # Handle voice and audio messages
        self.application.add_handler(MessageHandler(filters.VOICE | filters.AUDIO, handle_message))

        self.application.add_error_handler(self.error_handler)

    @staticmethod
    async def post_init(application: Application) -> None:
        # Set bot commands for UI autocomplete
        commands = [
            BotCommand("start", "🚀 Start chatting with me!"),
            BotCommand("clear", "🔄 Clear current chat session"),
            BotCommand("clear_memory", "🧠 Clear all memories & start fresh"),
            BotCommand("addsticker", "➕ Add a new sticker to your collection"), # Added command
        ]
        # Set commands with explicit scope to ensure they appear
        await application.bot.set_my_commands(commands)
        
        # Also set commands for private chats specifically
        await application.bot.set_my_commands(
            commands, 
            scope=BotCommandScopeAllPrivateChats()
        )
        
        logger.info("Bot commands registered successfully!")
        logger.info(f"Registered {len(commands)} commands: {[cmd.command for cmd in commands]}")
        logger.info("Bot successfully initialized!")

    async def _send_proactive_message(self, user_id: int, message: str):
        """Send a proactive message (reminder) to a user."""
        try:
            # Always send reminders to private chat with the user
            await self.application.bot.send_message(
                chat_id=user_id,
                text=message,
                parse_mode='HTML'
            )
            logger.info(f"✅ Sent reminder to user {user_id}")
        except Exception as e:
            logger.error(f"❌ Failed to send reminder to user {user_id}: {e}")
    
    @staticmethod
    async def error_handler(update: object, context: ContextTypes.DEFAULT_TYPE) -> None:
        logger.error("Exception while handling an update:", exc_info=context.error)

    async def run(self):
        logger.info("Starting Telegram bot...")

        # Start the simple scheduler
        await start_scheduler()

        async with self.application:
            await self.application.start()
            await self.application.updater.start_polling(
                allowed_updates=Update.ALL_TYPES,
                drop_pending_updates=True  # Ignore old messages
            )

            # Keep running until stopped
            import asyncio
            try:
                while True:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                pass

    async def shutdown(self):
        logger.info("Shutting down Telegram bot...")

        # Stop the simple scheduler
        await stop_scheduler()

        await self.ai_agent.cleanup()
        await self.application.updater.stop()
        await self.application.stop()
        await self.application.shutdown()

        logger.info("Bot stopped")
