import logging
import re
import sqlite3 # For catching specific database errors

from telegram import Update, Reply<PERSON><PERSON>board<PERSON>emove
from telegram.ext import (
    ContextTypes,
    CommandHandler,
    MessageHandler,
    filters,
    ConversationHandler,
)

from src.utils.whitelist import get_authorization_info
from src.utils.config import ADMIN_USERS # Changed from STICKER_ADMIN_USERS
from src.utils.database import DatabaseManager

logger = logging.getLogger(__name__)

# Define states for ConversationHandler
STICKER, EMOJIS = range(2)

# --- Command Handlers ---

async def add_sticker_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """Starts the conversation to add a new sticker."""
    user_id = update.effective_user.id
    user_info = get_authorization_info(update) # For logging, uses effective_user

    if user_id not in ADMIN_USERS: # Changed from STICKER_ADMIN_USERS
        logger.warning(f"User {user_info} (ID: {user_id}) attempted to use /addsticker without authorization. Denied.")
        await update.message.reply_text(
            "Sorry, you are not authorized to use this command."
            "Please contact the administrator if you believe this is a mistake."
        )
        return ConversationHandler.END

    logger.info(f"User {user_info} initiated /addsticker command")
    await update.message.reply_text(
        "Please send the sticker you want to add.\n"
        "Send /cancel at any time to stop."
    )
    return STICKER

async def receive_sticker_handler(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """Handles the sticker message from the user."""
    user_info = get_authorization_info(update) # For logging
    sticker = update.message.sticker

    if not sticker or not sticker.file_id:
        logger.warning(f"{user_info}: Message received in receive_sticker_handler is not a valid sticker or has no file_id.")
        await update.message.reply_text(
            "That doesn't seem to be a sticker. Please send a sticker, or /cancel."
        )
        return STICKER # Stay in the same state

    context.user_data['sticker_file_id'] = sticker.file_id
    context.user_data['sticker_set_name'] = sticker.set_name # Might be None for some stickers

    logger.info(f"{user_info}: Received sticker with file_id {sticker.file_id} from set {sticker.set_name}")

    await update.message.reply_text(
        "Got the sticker! Now, please send me a comma-separated list of emojis that describe this sticker.\n"
        "For example: 😂,🎉,👍"
    )
    return EMOJIS

async def invalid_sticker_handler(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """Handles non-sticker messages when a sticker is expected."""
    user_info = get_authorization_info(update) # For logging
    logger.info(f"{user_info}: Sent text instead of sticker in STICKER state.")
    await update.message.reply_text(
        "Please send a sticker, not text. If you want to cancel, send /cancel."
    )
    return STICKER # Remain in STICKER state

async def receive_emojis_handler(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """Handles the emoji list message from the user and saves the sticker."""
    user_info = get_authorization_info(update)
    user_id = update.effective_user.id

    sticker_file_id = context.user_data.get('sticker_file_id')
    sticker_set_name = context.user_data.get('sticker_set_name', "") # Default to empty if not present

    if not sticker_file_id:
        logger.error(f"{user_info}: sticker_file_id not found in user_data. This shouldn't happen.")
        await update.message.reply_text("An unexpected error occurred. Please try adding the sticker again using /addsticker.")
        context.user_data.clear()
        return ConversationHandler.END

    raw_emojis_text = update.message.text
    # Basic emoji validation: split by comma, trim whitespace.
    # A more robust regex could be used, but this is simple.
    # For example, to check if all characters are emojis: `all(emoji.isemoji(char) for char in stripped_emoji)`
    # but `emoji` library would be an external dependency.
    # For now, we just clean up the list.
    emojis_list = [emoji.strip() for emoji in raw_emojis_text.split(',') if emoji.strip()]

    if not emojis_list:
        await update.message.reply_text(
            "You didn't provide any emojis. Please provide a comma-separated list of emojis.\n"
            "For example: 😂,🎉,👍"
        )
        return EMOJIS # Stay in EMOJIS state

    emojis_str = ",".join(emojis_list)

    logger.info(f"{user_info}: Received emojis '{emojis_str}' for sticker {sticker_file_id}")

    try:
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                """
                INSERT INTO stickers (sticker_file_id, sticker_set_name, emojis, user_id)
                VALUES (?, ?, ?, ?)
                """,
                (sticker_file_id, sticker_set_name if sticker_set_name else None, emojis_str, user_id)
            )
            conn.commit()

        logger.info(f"{user_info}: Sticker {sticker_file_id} with emojis '{emojis_str}' successfully added for user {user_id}.")
        await update.message.reply_text(
            f"Sticker successfully added with emojis: {emojis_str}!\n"
            "You can now try searching for it (once search is implemented).",
            reply_markup=ReplyKeyboardRemove()
        )
    except sqlite3.IntegrityError: # Catches UNIQUE constraint violation for sticker_file_id
        logger.warning(f"{user_info}: Attempted to add duplicate sticker_file_id: {sticker_file_id}")
        await update.message.reply_text(
            "This sticker has already been added to the database. Try a different one or /cancel.",
            reply_markup=ReplyKeyboardRemove()
        )
    except Exception as e:
        logger.error(f"{user_info}: Error saving sticker {sticker_file_id} to database: {e}", exc_info=True)
        await update.message.reply_text(
            "An error occurred while saving the sticker. Please try again later or contact an admin.",
            reply_markup=ReplyKeyboardRemove()
        )
    finally:
        context.user_data.clear()

    return ConversationHandler.END

async def cancel_add_sticker_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """Cancels the sticker adding process."""
    user_info = get_authorization_info(update)
    logger.info(f"User {user_info} canceled the add sticker process.")
    await update.message.reply_text(
        "Sticker adding process canceled.", reply_markup=ReplyKeyboardRemove()
    )
    context.user_data.clear()
    return ConversationHandler.END

async def add_sticker_timeout_handler(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """Handles conversation timeout if explicitly triggered by a timeout filter.
    Note: `conversation_timeout` in ConversationHandler ends the conversation automatically.
    This handler would be for sending a custom message if `MessageHandler(filters.TIMEOUT, ...)` was used.
    Given the current setup, this handler might not be directly invoked by `conversation_timeout` itself
    unless PTB's specific version/configuration routes it here.
    The primary purpose of `conversation_timeout` is to transition to `ConversationHandler.END`.
    """
    user_info = get_authorization_info(update) # Might be None if update is not available on timeout
    logger.info(f"Add sticker conversation for {user_info if user_info else 'unknown user'} timed out.")

    # Check if we can send a message. The update might be None or lack a message
    # if the timeout is handled internally by PTB without a specific event update.
    if update and update.effective_message:
        try:
            await update.effective_message.reply_text(
                "Adding sticker process timed out. Please try again using /addsticker.",
                reply_markup=ReplyKeyboardRemove()
            )
        except Exception as e:
            logger.error(f"Error sending timeout message for {user_info}: {e}")
    else:
        # This case is tricky. If `context.chat_data` or `context.user_data` has `chat_id`,
        # we could try `context.bot.send_message(chat_id=..., text=...)`.
        # However, `chat_id` might not always be available or reliable here.
        logger.warning("Add sticker timeout occurred, but no direct update to reply to. User was not notified by this handler.")

    context.user_data.clear()
    return ConversationHandler.END


# --- Conversation Handler Setup ---
add_sticker_conv_handler = ConversationHandler(
    entry_points=[CommandHandler("addsticker", add_sticker_command)],
    states={
        STICKER: [
            MessageHandler(filters.Sticker.ALL, receive_sticker_handler),
            MessageHandler(filters.TEXT & ~filters.COMMAND, invalid_sticker_handler),
        ],
        EMOJIS: [
            MessageHandler(filters.TEXT & ~filters.COMMAND, receive_emojis_handler)
        ],
    },
    fallbacks=[
        CommandHandler("cancel", cancel_add_sticker_command),
        # Potentially add a MessageHandler(filters.TIMEOUT, add_sticker_timeout_handler) if needed
        # and if filters.TIMEOUT is a thing in the version being used.
        # For now, timeout is handled by conversation_timeout setting.
    ],
    conversation_timeout=5 * 60,  # 5 minutes
    # per_message=False, # Default, good for most cases
    # name="add_sticker_conversation", # Optional: for debugging
)

# Example of how this handler might be added to the application in main.py:
# from src.telegram.commands.add_sticker import add_sticker_conv_handler
# application.add_handler(add_sticker_conv_handler)

logger.info("Add sticker command handler loaded.")

# Instantiate DatabaseManager - consider dependency injection or a global instance if preferred
db_manager = DatabaseManager()
