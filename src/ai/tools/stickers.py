import logging
import random
import sqlite3
from typing import Optional, List, Dict, Any

from agno.tools import tool
from src.utils.database import DatabaseManager

logger = logging.getLogger(__name__)

# --- Global Context Variables ---
_bot_instance: Optional[Any] = None # Should be an instance of telegram.Bot
CURRENT_USER_ID: Optional[int] = None # User ID, will be used as chat_id for sending sticker

# --- Helper Functions ---

async def _find_sticker_by_emoji(emoji: str, db_manager: DatabaseManager) -> Optional[str]:
    """
    Finds a sticker_file_id by a given emoji.
    If multiple stickers match, one is chosen randomly.
    """
    target_emoji = emoji.strip()
    if not target_emoji:
        return None

    matching_sticker_file_ids: List[str] = []
    try:
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            # Fetch all stickers. This could be optimized if the DB grows very large,
            # e.g., by a more complex SQL query if emojis are stored differently
            # or if SQLite's JSON1 extension was used for emojis.
            # For now, client-side filtering is acceptable for a moderate number of stickers.
            cursor.execute("SELECT sticker_file_id, emojis FROM stickers")
            for row in cursor.fetchall():
                sticker_file_id = row["sticker_file_id"]
                stored_emojis_str = row["emojis"]
                if stored_emojis_str:
                    # Split the comma-separated emojis and trim whitespace
                    sticker_emojis = [e.strip() for e in stored_emojis_str.split(',') if e.strip()]
                    if target_emoji in sticker_emojis:
                        matching_sticker_file_ids.append(sticker_file_id)

        if matching_sticker_file_ids:
            return random.choice(matching_sticker_file_ids)
        else:
            logger.debug(f"No sticker found for emoji: {target_emoji}")
            return None
    except sqlite3.Error as e:
        logger.error(f"Database error while searching for sticker with emoji '{target_emoji}': {e}", exc_info=True)
        return None
    except Exception as e:
        logger.error(f"Unexpected error in _find_sticker_by_emoji for emoji '{target_emoji}': {e}", exc_info=True)
        return None

# --- Sticker Tool ---

@tool(show_result=False, requires_confirmation=False)
async def send_sticker_tool(emoji_keyword: str) -> Dict[str, Any]:
    """
    Sends a sticker to the current user (chat) based on an emoji keyword.
    The agent should provide a single emoji character as the keyword.
    The user_id (acting as chat_id for direct messages) and bot instance are set via global context.
    """
    logger.info(f"Attempting to send sticker for emoji '{emoji_keyword}' to user_id: {CURRENT_USER_ID}")

    if CURRENT_USER_ID is None:
        logger.error("send_sticker_tool: CURRENT_USER_ID is not set.")
        return {"success": False, "error": "User ID (chat_id) context not available."}

    if _bot_instance is None:
        logger.error("send_sticker_tool: _bot_instance is not set.")
        return {"success": False, "error": "Bot instance not available."}

    db_manager = DatabaseManager() # Consider if this should be passed or globally available

    cleaned_emoji = emoji_keyword.strip()
    if not cleaned_emoji:
        logger.warning("send_sticker_tool: Empty emoji_keyword provided.")
        return {"success": False, "error": "No emoji keyword provided."}

    sticker_file_id = await _find_sticker_by_emoji(cleaned_emoji, db_manager) # This can remain async if db becomes async

    if not sticker_file_id:
        logger.info(f"No sticker found for emoji: {cleaned_emoji} for user {CURRENT_USER_ID}")
        return {"success": False, "error": f"No sticker found for emoji: {cleaned_emoji}"}

    try:
        logger.info(f"Sending sticker {sticker_file_id} for emoji '{cleaned_emoji}' to user {CURRENT_USER_ID}")
        await _bot_instance.send_sticker(chat_id=CURRENT_USER_ID, sticker=sticker_file_id)
        logger.info(f"Successfully sent sticker {sticker_file_id} for '{cleaned_emoji}' to user {CURRENT_USER_ID}")
        return {"success": True, "message": f"Sticker for {cleaned_emoji} sent to user {CURRENT_USER_ID}."}
    except Exception as e:
        logger.error(f"Failed to send sticker {sticker_file_id} for '{cleaned_emoji}' to user {CURRENT_USER_ID}: {e}", exc_info=True)
        return {"success": False, "error": f"Failed to send sticker: {str(e)}"}

# --- Tool Export ---
STICKER_TOOLS = [send_sticker_tool]

logger.info("Sticker tools loaded.")

# --- Functions to inject context (called by the agent/tool processor) ---

def set_bot_instance(bot_instance: Any):
    """Sets the bot_instance for the sticker tool."""
    global _bot_instance
    _bot_instance = bot_instance
    logger.debug(f"Sticker tool: bot_instance provided: {_bot_instance is not None}")

def clear_bot_instance():
    """Clears the bot_instance for the sticker tool."""
    global _bot_instance
    _bot_instance = None
    logger.debug("Sticker tool: bot_instance cleared.")

def set_current_user_id(user_id: int):
    """Sets the CURRENT_USER_ID for sticker and potentially other tools."""
    global CURRENT_USER_ID
    CURRENT_USER_ID = user_id
    logger.debug(f"Sticker tool: CURRENT_USER_ID set to {user_id}")

def clear_current_user_id():
    """Clears the CURRENT_USER_ID."""
    global CURRENT_USER_ID
    CURRENT_USER_ID = None
    logger.debug("Sticker tool: CURRENT_USER_ID cleared.")

# Example instantiation of DatabaseManager (if not passed around)
# db_manager = DatabaseManager() # Or get it from a shared context


def get_unique_available_emojis() -> list[str]:
    """
    Fetches all unique emojis available from the stickers table.
    """
    db_manager = DatabaseManager()
    unique_emojis_set = set()
    try:
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT emojis FROM stickers")
            all_sticker_emojis_rows = cursor.fetchall()

            for row in all_sticker_emojis_rows:
                if row['emojis']:
                    emojis_list = row['emojis'].split(',')
                    for emoji in emojis_list:
                        cleaned_emoji = emoji.strip()
                        if cleaned_emoji: # Ensure not empty after strip
                            unique_emojis_set.add(cleaned_emoji)

        logger.debug(f"Fetched unique available emojis: {list(unique_emojis_set)}")
        return list(unique_emojis_set)
    except sqlite3.Error as e:
        logger.error(f"Database error while fetching unique emojis: {e}", exc_info=True)
        return []
    except Exception as e:
        logger.error(f"Unexpected error while fetching unique emojis: {e}", exc_info=True)
        return []


# --- Sticker List for Prompt ---
def get_formatted_sticker_list() -> str:
    """
    Fetches all stickers from the database and formats them as a Markdown table
    for inclusion in the agent's system prompt.
    """
    db_manager = DatabaseManager() # Each call creates a new manager, consider passing if high frequency
    stickers_data = []
    try:
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT id, emojis, sticker_set_name FROM stickers ORDER BY id")
            rows = cursor.fetchall()
            for row in rows:
                stickers_data.append({
                    "id": row["id"],
                    "emojis": row["emojis"],
                    "set_name": row["sticker_set_name"] if row["sticker_set_name"] else "N/A"
                })
    except sqlite3.Error as e:
        logger.error(f"Database error while fetching sticker list: {e}", exc_info=True)
        return "" # Return empty string on error to not break the prompt

    if not stickers_data:
        # As per requirement, do not include "No stickers" message in the prompt part.
        # The agent will simply not have sticker info.
        return ""

    header = "List of stickers that you can send to user to make chat more real, use it on end of your message (only 1 per message):\n"
    table_header = "| ID (for reference) | Emojis (use one as keyword) | Set Name (for context) |\n"
    table_divider = "|--------------------|-----------------------------|------------------------|\n"

    table_rows = []
    for sticker in stickers_data:
        # Ensure emojis are clean for display, e.g. no leading/trailing commas if data is messy
        emojis_display = sticker['emojis'].strip(',')
        table_rows.append(f"| {sticker['id']}                  | {emojis_display:<27} | {sticker['set_name']}      |\n")
        # Adjusted spacing for better alignment in Markdown, might need tweaking based on typical content length

    if not table_rows: # Should not happen if stickers_data is not empty, but as a safeguard
        return ""

    return header + table_header + table_divider + "".join(table_rows)
