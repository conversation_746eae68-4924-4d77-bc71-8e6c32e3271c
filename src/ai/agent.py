import logging
import os

logger = logging.getLogger(__name__)

from agno.agent import Agent
from agno.memory.v2 import Memory, MemoryManager, SessionSummarizer
from agno.memory.v2.db.sqlite import SqliteMemoryDb
from agno.models.anthropic import Claude
from agno.models.openrouter import OpenRouter
from agno.models.openai import OpenAIChat
from agno.storage.sqlite import SqliteStorage
from agno.tools.firecrawl import FirecrawlTools
from agno.tools.mcp import MultiMCPTools
from agno.tools.youtube import YouTubeTools

from src.ai.query_processor import QueryProcessor
from src.ai.summarizer import LiteMemory, HISTORY_RUNS_KEEP
from src.ai.tools import SIMPLE_REMINDER_TOOLS
from src.ai.tools.stickers import STICKER_TOOLS, get_unique_available_emojis # Added get_unique_available_emojis
from src.utils.config import (
    CLAUDE_MODEL, CLAUDE_SYSTEM_PROMPT, ANTHROPIC_API_KEY, FIRECRAWL_API_KEY, OPENAI_API_KEY, OPENROUTER_API_KEY
)

logger = logging.getLogger(__name__)


class AIAgent:
    def __init__(self, agent: Agent, query_handler: QueryProcessor):
        self.agent = agent
        self.query_handler = query_handler

    async def process_query(self, query: str, chat_id: int, context, images=None, files=None, reply_to_message_id=None, user_id=None) -> None:
        await self.query_handler.process_query(query, chat_id, context, images, files, reply_to_message_id, user_id)

    async def cleanup(self):
        pass


def get_basic_agent(user_id: str, session_id: str, mcp_tools: MultiMCPTools) -> AIAgent:
    """
    Create and return a new AIAgent instance for a specific user and session.
    
    Args:
        user_id: User identifier
        session_id: Session identifier
        mcp_tools: MultiMCPTools instance
    
    Returns:
        AIAgent: Configured agent instance
    """
    db_file = ".tmp/agent.db"

    manager = MemoryManager(
        model=OpenAIChat(id="gpt-4o-mini", api_key=OPENAI_API_KEY),
    )

    summarizer = SessionSummarizer(
        model=OpenRouter(id="google/gemini-2.0-flash-001", api_key=OPENROUTER_API_KEY, max_tokens=8192),
    )

    memory_storage = LiteMemory(
        model=OpenAIChat(id="gpt-4o-mini", api_key=OPENAI_API_KEY),
        db=SqliteMemoryDb(table_name="user_memories", db_file=db_file),
        memory_manager=manager,
        summarizer=summarizer,
        debug_mode=True,
    )

    sessions_storage = SqliteStorage(
        table_name="sessions",
        db_file=db_file
    )

    # Fetch unique emojis for the system prompt
    unique_emojis = get_unique_available_emojis()
    emoji_prompt_segment = ""
    if unique_emojis:
        available_emojis_str = ", ".join(unique_emojis)
        emoji_prompt_segment = (
            f"\n\n<stickers_info>\n"
            f"You can send a sticker by using the `send_sticker_tool` with one of the following available emojis: {available_emojis_str}. "
            f"It will make chat more friendly and fun. Each emoji has many stickers, so you can use the same emoji multiple times."
            f"Use a sticker only once at the end of your message if appropriate. Do not mention the tool call in your response to the user.\n"
            f"\n</stickers_info>"
        )

    final_system_prompt = CLAUDE_SYSTEM_PROMPT + emoji_prompt_segment

    agent = Agent(
        agent_id="default",
        user_id=user_id,
        session_id=session_id,
        model=Claude(
            id=CLAUDE_MODEL,
            api_key=ANTHROPIC_API_KEY,
            max_tokens=8192,
            cache_system_prompt=True,
            default_headers={
                "anthropic-beta": "files-api-2025-04-14,pdfs-2024-09-25"
            }
        ),
        tools=[
            {
                "type": "web_search_20250305",
                "name": "web_search",
                "max_uses": 5,
            },
            FirecrawlTools(api_key=FIRECRAWL_API_KEY),
            YouTubeTools(languages=['en', 'ru', 'uk'], get_video_captions=False),
            mcp_tools,
            *SIMPLE_REMINDER_TOOLS,  # Add simple reminder CRUD tools
            *STICKER_TOOLS,          # Add sticker tools
        ],
        markdown=False,
        show_tool_calls=False,
        tool_call_limit=50,
        instructions=final_system_prompt + """
<telegram_html_formatting>
You can use these HTML tags for message formatting:
• <b>bold text</b> or <strong>bold text</strong>
• <i>italic text</i> or <em>italic text</em>
• <u>underlined text</u> or <ins>underlined text</ins>
• <s>strikethrough text</s> or <del>strikethrough text</del>
• <code>inline code</code>
• <pre>preformatted code block</pre>
• <pre><code class="language-python">syntax highlighted code</code></pre>
• <a href="https://example.com">link text</a>
• <blockquote>quoted text</blockquote>
• <tg-spoiler>spoiler text</tg-spoiler>

Do NOT use unsupported HTML tags like <br>, <ul>, <li>, <div>, etc. as they will cause parsing errors.
Characters &, <, > will be automatically escaped, so you don't need to escape them manually.
</telegram_html_formatting>

<reminders>
You can create, update, delete, and list reminders using tools for it.
It automatically linked to user that currently speaking with you.
Dont dell id of reminders or smth like that, just use tools. (Only if user asks about it for testing purposes smth like that)
</reminders>
        """,
        debug_mode=True,  # TODO config
        add_datetime_to_instructions=True,
        memory=memory_storage,
        enable_agentic_memory=True,
        enable_session_summaries=True,
        storage=sessions_storage,
        add_history_to_messages=True,
        num_history_runs=HISTORY_RUNS_KEEP,  # How many last messages will be in the chat history
        read_chat_history=True,  # Added tool to read chat history, but better try to avoid it
    )

    query_handler = QueryProcessor(agent)
    return AIAgent(agent, query_handler)