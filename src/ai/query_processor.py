import asyncio
import logging
import time
from typing import List, Optional

from agno.agent import Agent
from agno.media import Image, File
from agno.run.response import RunEvent
from agno.tools import FunctionCall
from anthropic import APIStatusError
from telegram.constants import ChatAction
from telegram.ext import ContextTypes

from src.messaging import MessageFormatter
from src.messaging import MessageProcessor
from src.ai.tools import simple_reminders
from src.ai.tools.stickers import ( # Updated imports
    set_bot_instance, clear_bot_instance,
    set_current_user_id, clear_current_user_id
    # get_formatted_sticker_list # Removed this import
)
from src.ai.tools import TOOLS_HIDELIST # Updated import location

FAST_UPDATE_CHARS = 150  # First N characters update quickly
FAST_UPDATE_INTERVAL = 0.5  # Update interval for first N characters
NORMAL_UPDATE_INTERVAL = 2.0  # Update interval for the rest of the message
TOKENS_LIMIT_TO_SUMMARIZE = 4000  # Limit for summarizing the chat history

logger = logging.getLogger(__name__)


class QueryProcessor:
    agent: Agent

    def __init__(self, agent: Agent):
        self.agent = agent

    async def process_query(self, query: str, chat_id: int, context: ContextTypes.DEFAULT_TYPE, images: Optional[List[Image]] = None, files: Optional[List[File]] = None, reply_to_message_id: Optional[int] = None, user_id: int = None) -> None:
        """
        Process a user query and stream the results back.
        
        Args:
            query: The text query from the user
            chat_id: The chat ID for the conversation
            context: The Telegram context
            images: Optional list of Image objects to include with the query
            files: Optional list of File objects to include with the query
            reply_to_message_id: Optional message ID to reply to
            user_id: User ID for reminder tools
        """
        logger.info(f"Processing query for chat_id {chat_id}: {query[:100]}...")
        if images:
            logger.info(f"Query includes {len(images)} images")
        if files:
            logger.info(f"Query includes {len(files)} files")

        message_handler = MessageProcessor(context, chat_id, reply_to_message_id)
        formatter = MessageFormatter()

        try:
            await message_handler.send_initial_message("💭")

            if not message_handler.message_id:
                return

            await self._process_stream(query, chat_id, context, message_handler, formatter, images, files, reply_to_message_id, user_id)

        except APIStatusError as e:
            if e.status_code == 429:
                await message_handler.send_error_message("Rate limit exceeded. Please try again later.")
        except Exception as e:
            logger.error(f"Error processing query: {e}", exc_info=True)

            # TODO detect if error is ApiErrorType, and handle it better

            await message_handler.send_error_message(f"An unexpected error occurred: {type(e).__name__}")

    async def _process_stream(self, query: str, chat_id: int, context: ContextTypes.DEFAULT_TYPE,
                              message_processor: MessageProcessor, formatter: MessageFormatter,
                              images: Optional[List[Image]] = None, files: Optional[List[File]] = None, reply_to_message_id: Optional[int] = None, user_id: int = None) -> None:
        last_update_time = 0
        current_text_buffer = ""  # Buffer for accumulating text
        processed_tool_ids = set()  # Track tool calls to prevent duplicates
        total_text_length = 0  # Track total text length
        need_summarize = False
        has_visible_content = False  # Track if any visible content was generated
        has_any_tool_calls = False  # Track if any tool calls occurred

        # Inject user_id for reminder tools
        if user_id:
            simple_reminders.CURRENT_USER_ID = user_id

        await context.bot.send_chat_action(chat_id, ChatAction.TYPING)

        # Extract session_id and user_id from the agent
        # These were set when the agent was created in get_basic_agent()
        session_id = getattr(self.agent, 'session_id', None) or f"fallback_{chat_id}"
        # user_id is passed as an argument, use that one primarily. Fallback to agent's user_id or chat_id.
        effective_user_id = user_id if user_id is not None else (getattr(self.agent, 'user_id', None) or str(chat_id))
        
        # The sticker list is now part of the agent's system prompt from initialization.
        # No need to dynamically prepend it here.
        final_query = query # Use the original query directly

        # Combine images and files for the agent call
        media_items = []
        if images:
            media_items.extend(images)
        if files:
            media_items.extend(files)
        
        # Prepare arguments for arun
        arun_kwargs = {
            "stream": True,
            "stream_intermediate_steps": True,
            "user_id": effective_user_id, # Use effective_user_id
            "session_id": session_id,
        }
        
        # Add media parameters only if they exist
        if images:
            arun_kwargs["images"] = images
        if files:
            arun_kwargs["files"] = files
        
        logger.info(f"🚀 Calling agent.arun with: images={len(images) if images else 0}, files={len(files) if files else 0}, user_id={effective_user_id}")

        # Set bot instance and user ID for tools that need them
        set_bot_instance(context.bot)
        set_current_user_id(effective_user_id) # effective_user_id is appropriate here

        try:
            # Add debug logging to see what's being sent to Claude API
            logger.debug(f"Chat {chat_id}: Calling agent.arun with query: {final_query[:100]}...")
            logger.debug(f"Chat {chat_id}: Agent session_id: {session_id}, user_id: {effective_user_id}")

            # Try to get and log the message history before sending to Claude
            try:
                # Get the agent's messages for debugging
                if hasattr(self.agent, 'memory') and hasattr(self.agent.memory, 'get_messages'):
                    messages = self.agent.memory.get_messages(session_id=session_id, user_id=str(effective_user_id))
                    logger.debug(f"Chat {chat_id}: Current message history length: {len(messages) if messages else 0}")
                    if messages:
                        for i, msg in enumerate(messages[-5:]):  # Log last 5 messages
                            content_preview = str(msg.content)[:100] if hasattr(msg, 'content') else 'No content'
                            logger.debug(f"Chat {chat_id}: Message {i}: role={getattr(msg, 'role', 'unknown')}, content='{content_preview}...'")
            except Exception as debug_e:
                logger.debug(f"Chat {chat_id}: Could not get message history for debugging: {debug_e}")

            response_stream = await self.agent.arun(final_query, **arun_kwargs) # Use final_query

            async for chunk in response_stream:
                current_time = time.time()
                logger.debug(
                    f"Chat {chat_id} Chunk: {chunk.event} | {chunk.content_type} | Data: {str(chunk.content)[:50]}... | Extra: {chunk.extra_data}")

                should_update_message = False
                is_tool_event = False

                if chunk.event == RunEvent.run_response and chunk.content_type == "str" and chunk.content:
                    # Accumulate text in buffer
                    current_text_buffer += chunk.content
                    total_text_length += len(chunk.content)
                    has_visible_content = True  # Mark that we have visible text content

                    # Determine update interval based on total text length
                    update_interval = FAST_UPDATE_INTERVAL if total_text_length <= FAST_UPDATE_CHARS else NORMAL_UPDATE_INTERVAL

                    # Check if it's time to update
                    if current_time - last_update_time >= update_interval:
                        if current_text_buffer: # Ensure buffer has content before adding
                            formatter.add_text(current_text_buffer)
                            current_text_buffer = ""
                        should_update_message = True

                elif chunk.event == RunEvent.tool_call_started:
                    logger.info(f"Chat {chat_id}: Tool call started event.")

                    # Flush any pending text before adding tool
                    if current_text_buffer:
                        formatter.add_text(current_text_buffer)
                        current_text_buffer = ""

                    if chunk.tools:
                        for tool in chunk.tools:  # type: FunctionCall
                            tool_name = tool.tool_name if hasattr(tool, 'tool_name') else 'Unknown Tool'
                            tool_call_id = tool.tool_call_id if hasattr(tool, 'tool_call_id') else f"{tool_name}-{len(processed_tool_ids)}"
                            has_any_tool_calls = True  # Mark that we have tool calls

                            # Only process if we haven't seen this exact tool call
                            tool_key = f"{tool_call_id}:{True}"
                            if tool_key not in processed_tool_ids:
                                if tool_name not in TOOLS_HIDELIST: # Check hidelist
                                    processed_tool_ids.add(tool_key)
                                    # Create and add tool component
                                    formatter.add_tool(tool_name, tool_call_id)
                                    should_update_message = True
                                    is_tool_event = True  # Tool events update immediately
                                    has_visible_content = True  # Visible tool calls count as visible content
                                else:
                                    logger.info(f"Chat {chat_id}: Tool call {tool_name} hidden from user output.")

                elif chunk.event == RunEvent.tool_call_completed:
                    logger.info(f"Chat {chat_id}: Tool call completed event.")

                    # Flush any pending text before updating tool
                    if current_text_buffer:
                        formatter.add_text(current_text_buffer)
                        current_text_buffer = ""

                    if chunk.tools:
                        for tool in chunk.tools:  # type: FunctionCall
                            tool_name = tool.tool_name if hasattr(tool, 'tool_name') else 'Unknown Tool'
                            tool_call_id = tool.tool_call_id if hasattr(tool, 'tool_call_id') else f"{tool_name}-{len(processed_tool_ids)}"
                            is_error = getattr(tool, 'tool_call_error', False)

                            # Only process if we haven't seen this exact tool completion
                            tool_key = f"{tool_call_id}:completed"
                            # We need to add to processed_tool_ids even if hidden to track it,
                            # but only update formatter if not hidden.
                            if tool_key not in processed_tool_ids:
                                processed_tool_ids.add(tool_key) # Add to processed_tool_ids to avoid reprocessing if seen again
                                if tool_name not in TOOLS_HIDELIST: # Check hidelist
                                    # Update tool status
                                    formatter.update_tool(tool_call_id, True, is_error)
                                    should_update_message = True
                                    is_tool_event = True  # Tool events update immediately
                                    has_visible_content = True  # Visible tool completions count as visible content
                                else:
                                    logger.info(f"Chat {chat_id}: Tool completion {tool_name} hidden from user output.")
                elif chunk.event == RunEvent.run_completed:
                    total_tokens = sum(message.metrics.total_tokens for message in chunk.messages)

                    print(f"Chat {chat_id} | Tokens: {total_tokens}")

                    # if total_tokens > TOKENS_LIMIT_TO_SUMMARIZE:
                    #     need_summarize = True

                # Update the message if needed
                if should_update_message:
                    # If an update is flagged (e.g. by interval, or by a tool event)
                    # ensure any pending text in current_text_buffer is added to the formatter first.
                    if current_text_buffer:
                        formatter.add_text(current_text_buffer)
                        current_text_buffer = ""

                    formatted_content_update = formatter.format_message()
                    if formatted_content_update: # Only send update to Telegram if there's actual content
                        await message_processor.update_message(formatted_content_update)
                        last_update_time = time.time()

                    # Keep typing indicator active
                    if is_tool_event or total_text_length % 500 == 0: # Check is_tool_event if defined, or remove
                        await context.bot.send_chat_action(chat_id, ChatAction.TYPING)

                await asyncio.sleep(0.05)

            # Handle any leftover text at the end of the stream
            if current_text_buffer:
                formatter.add_text(current_text_buffer)

            formatted_content = formatter.format_message()

            # ---- ENHANCED LOGIC START ----
            # If the formatter.format_message() result is empty or whitespace-only,
            # it means the assistant effectively said nothing (e.g., only hidden tool calls occurred and no text output from AI).
            # To prevent an empty assistant message in the history (which Claude rejects),
            # we add a minimal text response.
            #
            # This is especially important when:
            # 1. Only hidden tools (like send_sticker_tool) were executed
            # 2. No visible text content was generated
            # 3. The conversation turn would result in an empty assistant message
            if not formatted_content.strip():
                if has_any_tool_calls and not has_visible_content:
                    logger.info(f"Chat {chat_id}: Only hidden tool calls occurred with no visible content. Adding minimal response to prevent empty assistant message.")
                    minimal_response = "✅"  # Simple checkmark to indicate action completed
                else:
                    logger.info(f"Chat {chat_id}: Formatted content was empty or whitespace. Adding minimal response 'OK'.")
                    minimal_response = "OK"

                formatter.add_text(minimal_response) # Add this to the formatter
                formatted_content = formatter.format_message() # Re-format to include minimal response
            # ---- ENHANCED LOGIC END ----

            # Use the potentially modified formatted_content for the final message.
            final_content = formatted_content

            await message_processor.update_message(final_content)
            
            # Clear user_id for simple_reminders (already done for sticker tool via clear_current_user_id)
            if 'simple_reminders' in globals() and hasattr(simple_reminders, 'CURRENT_USER_ID'): # defensive check
                simple_reminders.CURRENT_USER_ID = None # Reset if applicable

        except Exception as e:
            logger.error(f"Error in _process_stream: {e}", exc_info=True)
            # Add specific logging for Claude API errors
            if "all messages must have non-empty content" in str(e):
                logger.error(f"Chat {chat_id}: Claude API rejected empty message. Session: {session_id}, User: {effective_user_id}")
                logger.error(f"Chat {chat_id}: has_any_tool_calls={has_any_tool_calls}, has_visible_content={has_visible_content}")
                logger.error(f"Chat {chat_id}: formatted_content length: {len(formatted_content) if 'formatted_content' in locals() else 'N/A'}")
            raise
        finally:
            # Clear bot instance and user ID context
            clear_bot_instance()
            clear_current_user_id()
            logger.debug(f"Chat {chat_id}: Bot instance and current user ID context cleared for tools.")

        # if True:
            # session_summary = self.agent.get_session_summary(
            #     user_id=str(chat_id), session_id=f"private_{chat_id}",
            # )
            #
            # print(f"Chat {chat_id} | Session summary: {session_summary}")
